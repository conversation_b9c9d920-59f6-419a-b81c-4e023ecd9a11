"""
Command approval system for security.

Provides granular control over which commands and operations
the AI agent can execute, with configurable approval policies.
"""

import re
from typing import Dict, List, Optional, Any, Callable, Awaitable
from dataclasses import dataclass
from enum import Enum
import asyncio

from ai_terminal.config.settings import get_settings
from ai_terminal.utils.logger import get_logger

logger = get_logger(__name__)


class ApprovalAction(str, Enum):
    """Approval actions."""
    ALLOW = "allow"
    DENY = "deny"
    PROMPT = "prompt"


@dataclass
class ApprovalRule:
    """Rule for command approval."""
    name: str
    pattern: str
    action: ApprovalAction
    description: str
    priority: int = 0  # Higher priority rules are checked first


class ApprovalManager:
    """Manages command approval policies."""
    
    def __init__(self, interactive: bool = True):
        """Initialize approval manager.
        
        Args:
            interactive: Whether to prompt user for approval
        """
        self.interactive = interactive
        self.settings = get_settings()
        self.rules: List[ApprovalRule] = []
        self.approval_callback: Optional[Callable[[str, Dict[str, Any]], Awaitable[bool]]] = None
        
        self._load_default_rules()
    
    def _load_default_rules(self):
        """Load default approval rules."""
        # High-risk commands that should always be denied or prompted
        self.rules.extend([
            ApprovalRule(
                name="destructive_rm",
                pattern=r"rm\s+(-rf|--recursive.*--force|-f.*-r)",
                action=ApprovalAction.PROMPT,
                description="Destructive file removal",
                priority=100
            ),
            ApprovalRule(
                name="sudo_commands",
                pattern=r"sudo\s+",
                action=ApprovalAction.PROMPT,
                description="Commands with elevated privileges",
                priority=90
            ),
            ApprovalRule(
                name="system_modification",
                pattern=r"(chmod|chown|mount|umount)\s+",
                action=ApprovalAction.PROMPT,
                description="System modification commands",
                priority=80
            ),
            ApprovalRule(
                name="network_access",
                pattern=r"(curl|wget|nc|netcat|ssh|scp|rsync)\s+",
                action=ApprovalAction.PROMPT,
                description="Network access commands",
                priority=70
            ),
            ApprovalRule(
                name="package_management",
                pattern=r"(apt|yum|dnf|pip|npm|yarn)\s+(install|remove|uninstall)",
                action=ApprovalAction.PROMPT,
                description="Package installation/removal",
                priority=60
            ),
            ApprovalRule(
                name="file_redirection",
                pattern=r">\s*/dev/",
                action=ApprovalAction.DENY,
                description="Dangerous file redirection",
                priority=95
            ),
        ])
        
        # Load custom rules from settings
        for cmd in self.settings.security.approval_required:
            self.rules.append(ApprovalRule(
                name=f"custom_{cmd}",
                pattern=rf"\b{re.escape(cmd)}\b",
                action=ApprovalAction.PROMPT,
                description=f"Custom approval required for {cmd}",
                priority=50
            ))
        
        # Sort rules by priority (highest first)
        self.rules.sort(key=lambda r: r.priority, reverse=True)
    
    async def request_approval(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        context: Optional[Any] = None
    ) -> bool:
        """Request approval for a tool execution.
        
        Args:
            tool_name: Name of the tool to execute
            arguments: Tool arguments
            context: Optional context (e.g., conversation context)
            
        Returns:
            True if approved, False if denied
        """
        # Check if approval is disabled
        if not self.settings.security.sandbox_enabled:
            return True
        
        # Special handling for different tools
        if tool_name == "execute_command":
            return await self._approve_command(arguments.get("command", ""))
        elif tool_name in ["write_file", "read_file"]:
            return await self._approve_file_operation(tool_name, arguments)
        else:
            # Default approval for other tools
            return True
    
    async def _approve_command(self, command: str) -> bool:
        """Approve a shell command."""
        if not command:
            return False
        
        logger.info(f"Checking approval for command: {command}")
        
        # Check against rules
        for rule in self.rules:
            if re.search(rule.pattern, command, re.IGNORECASE):
                logger.info(f"Command matched rule: {rule.name} -> {rule.action}")
                
                if rule.action == ApprovalAction.DENY:
                    logger.warning(f"Command denied by rule {rule.name}: {command}")
                    return False
                elif rule.action == ApprovalAction.PROMPT:
                    if self.interactive:
                        return await self._prompt_user_approval(
                            f"Execute command: {command}",
                            f"Rule: {rule.description}"
                        )
                    else:
                        logger.warning(f"Command requires approval but not interactive: {command}")
                        return False
                elif rule.action == ApprovalAction.ALLOW:
                    return True
        
        # No matching rules - allow by default
        return True
    
    async def _approve_file_operation(self, operation: str, arguments: Dict[str, Any]) -> bool:
        """Approve a file operation."""
        file_path = arguments.get("file_path", "")
        
        if not file_path:
            return False
        
        logger.info(f"Checking approval for {operation}: {file_path}")
        
        # Check for dangerous paths
        dangerous_paths = [
            "/etc/",
            "/bin/",
            "/sbin/",
            "/usr/bin/",
            "/usr/sbin/",
            "/boot/",
            "/sys/",
            "/proc/",
        ]
        
        for dangerous_path in dangerous_paths:
            if file_path.startswith(dangerous_path):
                if self.interactive:
                    return await self._prompt_user_approval(
                        f"{operation.title()}: {file_path}",
                        "This is a system file/directory"
                    )
                else:
                    logger.warning(f"File operation denied for system path: {file_path}")
                    return False
        
        # Check file extension for write operations
        if operation == "write_file":
            dangerous_extensions = [".sh", ".bat", ".exe", ".cmd", ".ps1"]
            for ext in dangerous_extensions:
                if file_path.endswith(ext):
                    if self.interactive:
                        return await self._prompt_user_approval(
                            f"Write executable file: {file_path}",
                            "This creates an executable file"
                        )
                    else:
                        logger.warning(f"Write operation denied for executable: {file_path}")
                        return False
        
        return True
    
    async def _prompt_user_approval(self, action: str, reason: str) -> bool:
        """Prompt user for approval with Rich UI."""
        if self.approval_callback:
            return await self.approval_callback(action, {"reason": reason})

        # Use Rich for better UI
        try:
            from rich.console import Console
            from rich.panel import Panel
            from rich.text import Text
            from rich.prompt import Confirm

            console = Console()

            # Create approval panel
            approval_text = Text()
            approval_text.append("🔒 SECURITY APPROVAL REQUIRED\n\n", style="bold red")
            approval_text.append("Action: ", style="bold")
            approval_text.append(f"{action}\n", style="white")
            approval_text.append("Reason: ", style="bold")
            approval_text.append(f"{reason}\n\n", style="yellow")
            approval_text.append("This action requires your explicit approval for security.", style="dim")

            panel = Panel(
                approval_text,
                title="Security Approval",
                border_style="red",
                padding=(1, 2),
            )
            console.print(panel)

            # Get user confirmation
            approved = Confirm.ask(
                "[bold red]Allow this action?[/bold red]",
                default=False,
                console=console
            )

            if approved:
                logger.info(f"User approved: {action}")
                console.print("[green]✅ Action approved[/green]")
            else:
                logger.info(f"User denied: {action}")
                console.print("[red]❌ Action denied[/red]")

            return approved

        except ImportError:
            # Fallback to simple console prompt
            print(f"\n🔒 APPROVAL REQUIRED")
            print(f"Action: {action}")
            print(f"Reason: {reason}")

            while True:
                response = input("Allow this action? [y/N]: ").strip().lower()
                if response in ['y', 'yes']:
                    logger.info(f"User approved: {action}")
                    return True
                elif response in ['n', 'no', '']:
                    logger.info(f"User denied: {action}")
                    return False
                else:
                    print("Please enter 'y' for yes or 'n' for no")
    
    def add_rule(self, rule: ApprovalRule):
        """Add a custom approval rule."""
        self.rules.append(rule)
        self.rules.sort(key=lambda r: r.priority, reverse=True)
        logger.info(f"Added approval rule: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """Remove an approval rule."""
        self.rules = [r for r in self.rules if r.name != rule_name]
        logger.info(f"Removed approval rule: {rule_name}")
    
    def set_approval_callback(self, callback: Callable[[str, Dict[str, Any]], Awaitable[bool]]):
        """Set a custom approval callback."""
        self.approval_callback = callback
    
    def get_rules(self) -> List[ApprovalRule]:
        """Get all approval rules."""
        return self.rules.copy()
    
    def is_command_safe(self, command: str) -> bool:
        """Check if a command is considered safe (synchronous version)."""
        for rule in self.rules:
            if re.search(rule.pattern, command, re.IGNORECASE):
                if rule.action == ApprovalAction.DENY:
                    return False
                elif rule.action == ApprovalAction.PROMPT:
                    return False  # Requires approval
        return True
