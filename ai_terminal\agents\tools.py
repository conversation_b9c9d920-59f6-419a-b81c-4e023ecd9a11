"""
Tool system for AI agent function calling.

Provides a registry of tools that the AI can call to interact with the system,
including shell commands, file operations, and custom tools.
"""

import asyncio
import json
import subprocess
import platform
from typing import Dict, List, Optional, Any, Callable, Awaitable
from dataclasses import dataclass
from pathlib import Path
import shlex
import os

from ai_terminal.utils.logger import get_logger, log_performance
from ai_terminal.config.settings import get_settings

logger = get_logger(__name__)


@dataclass
class ToolResult:
    """Result from tool execution."""
    success: bool
    content: str
    metadata: Optional[Dict[str, Any]] = None
    
    def dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "success": self.success,
            "content": self.content,
            "metadata": self.metadata or {},
        }


@dataclass
class ToolDefinition:
    """Definition of a tool for AI function calling."""
    name: str
    description: str
    parameters: Dict[str, Any]
    function: Callable[..., Awaitable[<PERSON>lR<PERSON>ult]]
    
    def to_openai_format(self) -> Dict[str, Any]:
        """Convert to OpenAI function calling format."""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.parameters,
            }
        }


class ToolRegistry:
    """Registry for AI tools and functions."""
    
    def __init__(self):
        self.tools: Dict[str, ToolDefinition] = {}
        self.settings = get_settings()
        self._register_default_tools()
    
    def register_tool(self, tool: ToolDefinition) -> None:
        """Register a tool in the registry."""
        self.tools[tool.name] = tool
        logger.debug(f"Registered tool: {tool.name}")
    
    def get_tool_definitions(self) -> List[Dict[str, Any]]:
        """Get all tool definitions in OpenAI format."""
        return [tool.to_openai_format() for tool in self.tools.values()]
    
    async def execute_tool(self, name: str, arguments: Dict[str, Any]) -> ToolResult:
        """Execute a tool by name with given arguments."""
        if name not in self.tools:
            return ToolResult(
                success=False,
                content=f"Unknown tool: {name}",
                metadata={"error": "tool_not_found"}
            )
        
        tool = self.tools[name]
        
        try:
            with log_performance(logger, f"tool_execution_{name}", **arguments):
                result = await tool.function(**arguments)
                logger.info(f"Tool {name} executed successfully")
                return result
        except Exception as e:
            logger.error(f"Tool {name} execution failed: {e}", exc_info=True)
            return ToolResult(
                success=False,
                content=f"Tool execution failed: {e}",
                metadata={"error": str(e), "tool": name}
            )
    
    def _register_default_tools(self) -> None:
        """Register default system tools."""
        
        # Shell command execution
        self.register_tool(ToolDefinition(
            name="execute_command",
            description="Execute a shell command and return the output",
            parameters={
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "The shell command to execute"
                    },
                    "working_directory": {
                        "type": "string",
                        "description": "Working directory for the command (optional)"
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "Timeout in seconds (default: 30)"
                    }
                },
                "required": ["command"]
            },
            function=self._execute_command
        ))
        
        # File reading
        self.register_tool(ToolDefinition(
            name="read_file",
            description="Read the contents of a file",
            parameters={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the file to read"
                    },
                    "encoding": {
                        "type": "string",
                        "description": "File encoding (default: utf-8)"
                    }
                },
                "required": ["file_path"]
            },
            function=self._read_file
        ))
        
        # File writing
        self.register_tool(ToolDefinition(
            name="write_file",
            description="Write content to a file",
            parameters={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Path to the file to write"
                    },
                    "content": {
                        "type": "string",
                        "description": "Content to write to the file"
                    },
                    "encoding": {
                        "type": "string",
                        "description": "File encoding (default: utf-8)"
                    },
                    "create_directories": {
                        "type": "boolean",
                        "description": "Create parent directories if they don't exist"
                    }
                },
                "required": ["file_path", "content"]
            },
            function=self._write_file
        ))
        
        # Directory listing
        self.register_tool(ToolDefinition(
            name="list_directory",
            description="List the contents of a directory",
            parameters={
                "type": "object",
                "properties": {
                    "directory_path": {
                        "type": "string",
                        "description": "Path to the directory to list"
                    },
                    "show_hidden": {
                        "type": "boolean",
                        "description": "Include hidden files and directories"
                    },
                    "recursive": {
                        "type": "boolean",
                        "description": "List recursively"
                    }
                },
                "required": ["directory_path"]
            },
            function=self._list_directory
        ))
        
        # File search
        self.register_tool(ToolDefinition(
            name="search_files",
            description="Search for files matching a pattern",
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {
                        "type": "string",
                        "description": "Search pattern (glob or regex)"
                    },
                    "directory": {
                        "type": "string",
                        "description": "Directory to search in (default: current)"
                    },
                    "recursive": {
                        "type": "boolean",
                        "description": "Search recursively"
                    }
                },
                "required": ["pattern"]
            },
            function=self._search_files
        ))
        
        # System information
        self.register_tool(ToolDefinition(
            name="get_system_info",
            description="Get system information including OS, hardware, and environment",
            parameters={
                "type": "object",
                "properties": {
                    "detailed": {
                        "type": "boolean",
                        "description": "Whether to include detailed information (default: false)"
                    }
                }
            },
            function=self._get_system_info
        ))

        # Process management
        self.register_tool(ToolDefinition(
            name="list_processes",
            description="List running processes with optional filtering",
            parameters={
                "type": "object",
                "properties": {
                    "filter_name": {
                        "type": "string",
                        "description": "Filter processes by name pattern"
                    },
                    "show_details": {
                        "type": "boolean",
                        "description": "Show detailed process information (default: false)"
                    }
                }
            },
            function=self._list_processes
        ))

        # Network operations
        self.register_tool(ToolDefinition(
            name="check_network",
            description="Check network connectivity and status",
            parameters={
                "type": "object",
                "properties": {
                    "host": {
                        "type": "string",
                        "description": "Host to check connectivity to (default: google.com)"
                    },
                    "port": {
                        "type": "integer",
                        "description": "Port to check (default: 80)"
                    }
                }
            },
            function=self._check_network
        ))

        # File operations
        self.register_tool(ToolDefinition(
            name="copy_file",
            description="Copy a file from source to destination",
            parameters={
                "type": "object",
                "properties": {
                    "source": {
                        "type": "string",
                        "description": "Source file path"
                    },
                    "destination": {
                        "type": "string",
                        "description": "Destination file path"
                    },
                    "overwrite": {
                        "type": "boolean",
                        "description": "Whether to overwrite existing file (default: false)"
                    }
                },
                "required": ["source", "destination"]
            },
            function=self._copy_file
        ))

        # Environment variables
        self.register_tool(ToolDefinition(
            name="get_env_var",
            description="Get environment variable value",
            parameters={
                "type": "object",
                "properties": {
                    "name": {
                        "type": "string",
                        "description": "Environment variable name"
                    }
                },
                "required": ["name"]
            },
            function=self._get_env_var
        ))
    
    async def _execute_command(
        self,
        command: str,
        working_directory: Optional[str] = None,
        timeout: int = 30
    ) -> ToolResult:
        """Execute a shell command."""
        try:
            # Validate command against security settings
            if not self._is_command_safe(command):
                return ToolResult(
                    success=False,
                    content=f"Command blocked by security policy: {command}",
                    metadata={"error": "security_blocked", "command": command}
                )
            
            # Prepare command for execution
            if platform.system() == "Windows":
                cmd_args = ["cmd", "/c", command]
            else:
                cmd_args = ["sh", "-c", command]
            
            # Set working directory
            cwd = Path(working_directory) if working_directory else None
            if cwd and not cwd.exists():
                return ToolResult(
                    success=False,
                    content=f"Working directory does not exist: {working_directory}",
                    metadata={"error": "directory_not_found"}
                )
            
            # Execute command
            process = await asyncio.create_subprocess_exec(
                *cmd_args,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=cwd
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return ToolResult(
                    success=False,
                    content=f"Command timed out after {timeout} seconds",
                    metadata={"error": "timeout", "command": command}
                )
            
            # Format output
            output_parts = []
            if stdout:
                output_parts.append(f"STDOUT:\n{stdout.decode('utf-8', errors='replace')}")
            if stderr:
                output_parts.append(f"STDERR:\n{stderr.decode('utf-8', errors='replace')}")
            
            output = "\n\n".join(output_parts) if output_parts else "No output"
            
            return ToolResult(
                success=process.returncode == 0,
                content=output,
                metadata={
                    "command": command,
                    "return_code": process.returncode,
                    "working_directory": str(cwd) if cwd else None
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                content=f"Failed to execute command: {e}",
                metadata={"error": str(e), "command": command}
            )
    
    async def _read_file(self, file_path: str, encoding: str = "utf-8") -> ToolResult:
        """Read a file's contents."""
        try:
            path = Path(file_path)
            
            # Security check
            if not self._is_file_safe(path):
                return ToolResult(
                    success=False,
                    content=f"File access blocked by security policy: {file_path}",
                    metadata={"error": "security_blocked"}
                )
            
            if not path.exists():
                return ToolResult(
                    success=False,
                    content=f"File does not exist: {file_path}",
                    metadata={"error": "file_not_found"}
                )
            
            if not path.is_file():
                return ToolResult(
                    success=False,
                    content=f"Path is not a file: {file_path}",
                    metadata={"error": "not_a_file"}
                )
            
            # Check file size
            if path.stat().st_size > self.settings.security.max_file_size:
                return ToolResult(
                    success=False,
                    content=f"File too large: {file_path}",
                    metadata={"error": "file_too_large"}
                )
            
            content = path.read_text(encoding=encoding)
            
            return ToolResult(
                success=True,
                content=content,
                metadata={
                    "file_path": str(path),
                    "size": path.stat().st_size,
                    "encoding": encoding
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                content=f"Failed to read file: {e}",
                metadata={"error": str(e), "file_path": file_path}
            )
    
    async def _write_file(
        self,
        file_path: str,
        content: str,
        encoding: str = "utf-8",
        create_directories: bool = False
    ) -> ToolResult:
        """Write content to a file."""
        try:
            path = Path(file_path)
            
            # Security check
            if not self._is_file_safe(path):
                return ToolResult(
                    success=False,
                    content=f"File access blocked by security policy: {file_path}",
                    metadata={"error": "security_blocked"}
                )
            
            # Create parent directories if requested
            if create_directories:
                path.parent.mkdir(parents=True, exist_ok=True)
            
            path.write_text(content, encoding=encoding)
            
            return ToolResult(
                success=True,
                content=f"Successfully wrote {len(content)} characters to {file_path}",
                metadata={
                    "file_path": str(path),
                    "size": len(content),
                    "encoding": encoding
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                content=f"Failed to write file: {e}",
                metadata={"error": str(e), "file_path": file_path}
            )
    
    async def _list_directory(
        self,
        directory_path: str,
        show_hidden: bool = False,
        recursive: bool = False
    ) -> ToolResult:
        """List directory contents."""
        try:
            path = Path(directory_path)
            
            if not path.exists():
                return ToolResult(
                    success=False,
                    content=f"Directory does not exist: {directory_path}",
                    metadata={"error": "directory_not_found"}
                )
            
            if not path.is_dir():
                return ToolResult(
                    success=False,
                    content=f"Path is not a directory: {directory_path}",
                    metadata={"error": "not_a_directory"}
                )
            
            items = []
            
            if recursive:
                pattern = "**/*" if show_hidden else "**/[!.]*"
                for item in path.glob(pattern):
                    items.append(self._format_file_info(item))
            else:
                for item in path.iterdir():
                    if not show_hidden and item.name.startswith('.'):
                        continue
                    items.append(self._format_file_info(item))
            
            items.sort(key=lambda x: (not x["is_directory"], x["name"].lower()))
            
            # Format output
            output_lines = [f"Contents of {directory_path}:"]
            for item in items:
                type_indicator = "📁" if item["is_directory"] else "📄"
                size_info = f" ({item['size']} bytes)" if not item["is_directory"] else ""
                output_lines.append(f"{type_indicator} {item['name']}{size_info}")
            
            return ToolResult(
                success=True,
                content="\n".join(output_lines),
                metadata={
                    "directory_path": str(path),
                    "item_count": len(items),
                    "items": items
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                content=f"Failed to list directory: {e}",
                metadata={"error": str(e), "directory_path": directory_path}
            )
    
    async def _search_files(
        self,
        pattern: str,
        directory: str = ".",
        recursive: bool = True
    ) -> ToolResult:
        """Search for files matching a pattern."""
        try:
            path = Path(directory)
            
            if not path.exists():
                return ToolResult(
                    success=False,
                    content=f"Directory does not exist: {directory}",
                    metadata={"error": "directory_not_found"}
                )
            
            matches = []
            search_pattern = f"**/{pattern}" if recursive else pattern
            
            for match in path.glob(search_pattern):
                if match.is_file():
                    matches.append(self._format_file_info(match))
            
            matches.sort(key=lambda x: x["name"].lower())
            
            # Format output
            if not matches:
                output = f"No files found matching pattern: {pattern}"
            else:
                output_lines = [f"Found {len(matches)} files matching '{pattern}':"]
                for match in matches:
                    output_lines.append(f"📄 {match['path']} ({match['size']} bytes)")
                output = "\n".join(output_lines)
            
            return ToolResult(
                success=True,
                content=output,
                metadata={
                    "pattern": pattern,
                    "directory": str(path),
                    "match_count": len(matches),
                    "matches": matches
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                content=f"Failed to search files: {e}",
                metadata={"error": str(e), "pattern": pattern}
            )
    
    async def _get_system_info(self, detailed: bool = False) -> ToolResult:
        """Get system information."""
        try:
            info = {
                "platform": platform.platform(),
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "python_version": platform.python_version(),
            }

            # Try to get additional info with psutil
            try:
                import psutil
                info.update({
                    "cpu_count": psutil.cpu_count(),
                    "cpu_percent": psutil.cpu_percent(interval=1),
                    "memory_total": psutil.virtual_memory().total,
                    "memory_available": psutil.virtual_memory().available,
                    "memory_percent": psutil.virtual_memory().percent,
                })

                # Disk usage (handle different OS)
                disk_path = '/' if platform.system() != 'Windows' else 'C:\\'
                disk = psutil.disk_usage(disk_path)
                info["disk_usage"] = {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": (disk.used / disk.total) * 100
                }

                if detailed:
                    info.update({
                        "boot_time": psutil.boot_time(),
                        "network_interfaces": list(psutil.net_if_addrs().keys()),
                        "disk_partitions": [p.device for p in psutil.disk_partitions()],
                    })

            except ImportError:
                logger.warning("psutil not available, limited system info")

            # Format output
            output_lines = [
                f"System: {info['system']} {info['release']}",
                f"Platform: {info['platform']}",
                f"Machine: {info['machine']}",
                f"Processor: {info['processor']}",
                f"Python: {info['python_version']}",
            ]

            if 'cpu_count' in info:
                output_lines.extend([
                    f"CPU Cores: {info['cpu_count']}",
                    f"CPU Usage: {info.get('cpu_percent', 'N/A')}%",
                    f"Memory: {info['memory_available'] / 1024**3:.1f}GB available / {info['memory_total'] / 1024**3:.1f}GB total ({info['memory_percent']:.1f}% used)",
                    f"Disk: {info['disk_usage']['free'] / 1024**3:.1f}GB free / {info['disk_usage']['total'] / 1024**3:.1f}GB total ({info['disk_usage']['percent']:.1f}% used)",
                ])

                if detailed and 'network_interfaces' in info:
                    output_lines.extend([
                        f"Network Interfaces: {', '.join(info['network_interfaces'])}",
                        f"Disk Partitions: {', '.join(info['disk_partitions'])}",
                    ])

            return ToolResult(
                success=True,
                content="\n".join(output_lines),
                metadata=info
            )

        except Exception as e:
            return ToolResult(
                success=False,
                content=f"Failed to get system info: {e}",
                metadata={"error": str(e)}
            )

    async def _list_processes(self, filter_name: Optional[str] = None, show_details: bool = False) -> ToolResult:
        """List running processes."""
        try:
            import psutil

            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    proc_info = proc.info
                    if filter_name and filter_name.lower() not in proc_info['name'].lower():
                        continue

                    if show_details:
                        proc_info.update({
                            'cmdline': ' '.join(proc.cmdline()[:3]),  # First 3 args
                            'create_time': proc.create_time(),
                            'num_threads': proc.num_threads(),
                        })

                    processes.append(proc_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # Sort by CPU usage
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)

            # Format output
            if not processes:
                output = "No processes found"
                if filter_name:
                    output += f" matching '{filter_name}'"
            else:
                output_lines = [f"Found {len(processes)} processes:"]
                for proc in processes[:20]:  # Show top 20
                    line = f"PID {proc['pid']}: {proc['name']} ({proc['status']})"
                    if show_details:
                        line += f" - CPU: {proc.get('cpu_percent', 0):.1f}%, Memory: {proc.get('memory_percent', 0):.1f}%"
                        if proc.get('cmdline'):
                            line += f" - CMD: {proc['cmdline'][:50]}..."
                    output_lines.append(line)
                output = "\n".join(output_lines)

            return ToolResult(
                success=True,
                content=output,
                metadata={"process_count": len(processes), "processes": processes[:10]}
            )

        except ImportError:
            return ToolResult(
                success=False,
                content="psutil not available - cannot list processes",
                metadata={"error": "psutil_not_available"}
            )
        except Exception as e:
            return ToolResult(
                success=False,
                content=f"Failed to list processes: {e}",
                metadata={"error": str(e)}
            )

    async def _check_network(self, host: str = "google.com", port: int = 80) -> ToolResult:
        """Check network connectivity."""
        try:
            import socket
            import time

            start_time = time.time()

            # Create socket and test connection
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)

            try:
                result = sock.connect_ex((host, port))
                connected = result == 0
                response_time = (time.time() - start_time) * 1000

                if connected:
                    output = f"✅ Successfully connected to {host}:{port} in {response_time:.1f}ms"
                else:
                    output = f"❌ Failed to connect to {host}:{port} (error code: {result})"

                return ToolResult(
                    success=connected,
                    content=output,
                    metadata={
                        "host": host,
                        "port": port,
                        "connected": connected,
                        "response_time_ms": response_time,
                        "error_code": result if not connected else None
                    }
                )

            finally:
                sock.close()

        except Exception as e:
            return ToolResult(
                success=False,
                content=f"Network check failed: {e}",
                metadata={"error": str(e), "host": host, "port": port}
            )

    async def _copy_file(self, source: str, destination: str, overwrite: bool = False) -> ToolResult:
        """Copy a file from source to destination."""
        try:
            import shutil

            source_path = Path(source)
            dest_path = Path(destination)

            # Security checks
            if not self._is_file_safe(source_path) or not self._is_file_safe(dest_path):
                return ToolResult(
                    success=False,
                    content="File access blocked by security policy",
                    metadata={"error": "security_blocked"}
                )

            if not source_path.exists():
                return ToolResult(
                    success=False,
                    content=f"Source file does not exist: {source}",
                    metadata={"error": "source_not_found"}
                )

            if dest_path.exists() and not overwrite:
                return ToolResult(
                    success=False,
                    content=f"Destination file exists and overwrite is disabled: {destination}",
                    metadata={"error": "destination_exists"}
                )

            # Create destination directory if needed
            dest_path.parent.mkdir(parents=True, exist_ok=True)

            # Copy file
            shutil.copy2(source_path, dest_path)

            return ToolResult(
                success=True,
                content=f"Successfully copied {source} to {destination}",
                metadata={
                    "source": str(source_path),
                    "destination": str(dest_path),
                    "size": dest_path.stat().st_size
                }
            )

        except Exception as e:
            return ToolResult(
                success=False,
                content=f"Failed to copy file: {e}",
                metadata={"error": str(e), "source": source, "destination": destination}
            )

    async def _get_env_var(self, name: str) -> ToolResult:
        """Get environment variable value."""
        try:
            value = os.environ.get(name)

            if value is None:
                return ToolResult(
                    success=False,
                    content=f"Environment variable '{name}' not found",
                    metadata={"error": "env_var_not_found", "name": name}
                )

            # Mask sensitive values
            sensitive_patterns = ['key', 'secret', 'token', 'password', 'pass']
            is_sensitive = any(pattern in name.lower() for pattern in sensitive_patterns)

            display_value = value
            if is_sensitive and len(value) > 8:
                display_value = value[:4] + "*" * (len(value) - 8) + value[-4:]

            return ToolResult(
                success=True,
                content=f"{name}={display_value}",
                metadata={
                    "name": name,
                    "value": value if not is_sensitive else "[MASKED]",
                    "is_sensitive": is_sensitive
                }
            )

        except Exception as e:
            return ToolResult(
                success=False,
                content=f"Failed to get environment variable: {e}",
                metadata={"error": str(e), "name": name}
            )

    def _is_command_safe(self, command: str) -> bool:
        """Check if a command is safe to execute."""
        # Check against approval required commands
        for blocked_cmd in self.settings.security.approval_required:
            if blocked_cmd in command.lower():
                return False
        
        # Check against dangerous patterns
        import re
        for pattern in self.settings.security.approval_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                return False
        
        return True
    
    def _is_file_safe(self, path: Path) -> bool:
        """Check if file access is safe."""
        # Check file extension
        if path.suffix and path.suffix not in self.settings.security.allowed_extensions:
            return False
        
        # Check for path traversal
        try:
            path.resolve().relative_to(Path.cwd().resolve())
        except ValueError:
            return False
        
        return True
    
    def _format_file_info(self, path: Path) -> Dict[str, Any]:
        """Format file information."""
        stat = path.stat()
        return {
            "name": path.name,
            "path": str(path),
            "is_directory": path.is_dir(),
            "size": stat.st_size,
            "modified": stat.st_mtime,
        }
